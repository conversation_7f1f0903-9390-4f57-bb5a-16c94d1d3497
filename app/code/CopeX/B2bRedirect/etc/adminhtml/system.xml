<?xml version="1.0"?>
<!--
/**
 * CopeX B2B Redirect System Configuration
 *
 * @category  CopeX
 * @package   CopeX_B2bRedirect
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="copex" translate="label" sortOrder="200">
            <label>CopeX</label>
        </tab>
        <section id="copex_b2b_redirect" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>B2B Redirect</label>
            <tab>copex</tab>
            <resource>CopeX_B2bRedirect::config</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Settings</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable B2B Redirect</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable automatic redirection between B2B and B2C stores based on customer groups</comment>
                </field>
                <field id="customer_groups" translate="label comment" type="multiselect" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>B2B Customer Groups</label>
                    <source_model>Magento\Customer\Model\Config\Source\Group</source_model>
                    <comment>Select customer groups that should be redirected to B2B store</comment>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="b2b_base_url" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>B2B Base URL</label>
                    <comment>Base URL for B2B store (e.g., https://b2b.pros-pro.com)</comment>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="b2c_base_url" translate="label comment" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>B2C Base URL</label>
                    <comment>Base URL for B2C store (e.g., https://www.pros-pro.com)</comment>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="prospro_website_code" translate="label comment" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>ProsPro Website Code</label>
                    <comment>Website code for ProsPro website</comment>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="arfaian_website_code" translate="label comment" type="text" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Arfaian Website Code</label>
                    <comment>Website code for Arfaian website</comment>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
        <section id="sales">
            <group id="minimum_order">
                <field id="customer_group_rules" translate="label comment" type="textarea" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Minimum Order Values by Customer Group</label>
                    <comment><![CDATA[List of minimum order values for customer groups. Groups are comma-separated, followed by a colon and the price. One price per line! Example: 1,2,4:10. For groups not listed, the standard value above applies.]]></comment>
                </field>
            </group>
        </section>
    </system>
</config>
