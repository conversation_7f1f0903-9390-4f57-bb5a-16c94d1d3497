# CopeX B2B Redirect Module

## Overview

The CopeX B2B Redirect module is a Magento 2 module that automatically redirects customers between B2B and B2C stores based on their customer group membership. This module is a migration from the original Magento 1 `Mstage_B2b` module.

## Features

- **Automatic Redirection**: Redirects customers between B2B and B2C stores based on customer groups
- **Quote Transfer**: Transfers shopping cart items between stores during redirection
- **Minimum Order Configuration**: Allows setting minimum order values per customer group
- **Admin Configuration**: Provides comprehensive system configuration options

## Configuration

### Admin Configuration Path
`Stores > Configuration > CopeX > B2B Redirect`

### Configuration Options

1. **Enable B2B Redirect**: Enable/disable the module functionality
2. **B2B Customer Groups**: Select which customer groups should be redirected to B2B store
3. **B2B Base URL**: Base URL for the B2B store (e.g., https://b2b.pros-pro.com)
4. **B2C Base URL**: Base URL for the B2C store (e.g., https://www.pros-pro.com)
5. **ProsPro Website Code**: Website code for ProsPro website
6. **Arfaian Website Code**: Website code for Arfaian website

### Minimum Order Configuration
`Stores > Configuration > Sales > Minimum Order`

- **Minimum Order Values by Customer Group**: Configure minimum order values for specific customer groups using the format: `1,2,4:10` (groups 1,2,4 have minimum order of 10)

## How It Works

1. **Customer Session Initialization**: The module listens to the `customer_session_init` event
2. **Website Detection**: Determines the current website code
3. **Customer Group Check**: Checks if the customer belongs to configured B2B customer groups
4. **Redirection Logic**:
   - If on ProsPro website and customer is B2B → redirect to B2B store
   - If on Arfaian website and customer is not B2B → redirect to B2C store
5. **Quote Transfer**: Transfers cart items between stores during redirection

## Store ID Mapping

The module transfers quotes between specific store IDs:
- Store 1 → Store 3
- Store 2 → Store 4

## Installation

1. Copy the module files to `app/code/CopeX/B2bRedirect/`
2. Run the following commands:
   ```bash
   php bin/magento module:enable CopeX_B2bRedirect
   php bin/magento setup:upgrade
   php bin/magento cache:flush
   ```

## Migration Notes

This module replaces the Magento 1 `Mstage_B2b` module with the following key changes:

- **Namespace**: Changed from `Mstage_B2b` to `CopeX_B2bRedirect`
- **Event System**: Updated to use Magento 2 event observers
- **Configuration**: Migrated to Magento 2 system configuration format
- **Dependency Injection**: Uses Magento 2 DI container
- **Error Handling**: Improved error logging and exception handling

## Technical Details

### Files Structure
```
app/code/CopeX/B2bRedirect/
├── Helper/
│   └── Data.php
├── Observer/
│   └── CustomerSessionInitObserver.php
├── etc/
│   ├── acl.xml
│   ├── config.xml
│   ├── module.xml
│   ├── adminhtml/
│   │   └── system.xml
│   └── frontend/
│       └── events.xml
├── composer.json
├── registration.php
└── README.md
```

### Dependencies
- Magento_Store
- Magento_Customer
- Magento_Quote
- Magento_Checkout

## Support

For support and questions, please contact the CopeX development team.
