<?php
/**
 * CopeX B2B Redirect Controller Action Predispatch Observer
 *
 * @category  CopeX
 * @package   CopeX_B2bRedirect
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

namespace CopeX\B2bRedirect\Observer;

use Cope<PERSON>\B2bRedirect\Helper\Data as B2bHelper;
use Magento\Checkout\Model\Cart;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\Response\Http as HttpResponse;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\App\RequestInterface;
use Psr\Log\LoggerInterface;

class ControllerActionPredispatchObserver implements ObserverInterface
{
    /**
     * @var bool
     */
    protected $singletonFlag = false;

    /**
     * @var B2bHelper
     */
    protected $b2bHelper;

    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * @var Cart
     */
    protected $cart;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var HttpResponse
     */
    protected $response;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var RequestInterface
     */
    protected $request;

    /**
     * ControllerActionPredispatchObserver constructor.
     *
     * @param B2bHelper $b2bHelper
     * @param CustomerSession $customerSession
     * @param Cart $cart
     * @param StoreManagerInterface $storeManager
     * @param HttpResponse $response
     * @param RequestInterface $request
     * @param LoggerInterface $logger
     */
    public function __construct(
        B2bHelper $b2bHelper,
        CustomerSession $customerSession,
        Cart $cart,
        StoreManagerInterface $storeManager,
        HttpResponse $response,
        RequestInterface $request,
        LoggerInterface $logger
    ) {
        $this->b2bHelper = $b2bHelper;
        $this->customerSession = $customerSession;
        $this->cart = $cart;
        $this->storeManager = $storeManager;
        $this->response = $response;
        $this->request = $request;
        $this->logger = $logger;
    }

    /**
     * Execute observer
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        if (!$this->b2bHelper->isEnabled()) {
            return;
        }

        if ($this->singletonFlag) {
            return;
        }

        $this->singletonFlag = true;

        try {
            // Skip if customer is not logged in
            if (!$this->customerSession->isLoggedIn()) {
                return;
            }

            // Skip AJAX requests and admin requests
            if ($this->request->isAjax() || $this->request->getModuleName() === 'admin') {
                return;
            }

            $websiteCode = $this->storeManager->getWebsite()->getCode();
            $prosProWebsiteCode = $this->b2bHelper->getProsProWebsiteCode();
            $arfaianWebsiteCode = $this->b2bHelper->getArfaianWebsiteCode();

            if ($websiteCode == $prosProWebsiteCode && $this->isB2bCustomer($this->customerSession)) {
                $this->transferQuote();
                $redirectUrl = $this->b2bHelper->getRedirectUrl(true);
                $this->response->setRedirect($redirectUrl)->sendResponse();
                exit;
            } elseif ($websiteCode == $arfaianWebsiteCode && !$this->isB2bCustomer($this->customerSession)) {
                $redirectUrl = $this->b2bHelper->getRedirectUrl(false);
                $this->response->setRedirect($redirectUrl)->sendResponse();
                exit;
            }
        } catch (\Exception $e) {
            $this->logger->error('B2B Redirect Error: ' . $e->getMessage());
        }
    }

    /**
     * Check if customer is B2B customer
     *
     * @param CustomerSession $customerSession
     * @return bool
     */
    protected function isB2bCustomer($customerSession)
    {
        $groupId = $customerSession->getCustomerGroupId();
        $allowedCustomerGroups = $this->b2bHelper->getCustomerGroups();

        return in_array($groupId, $allowedCustomerGroups);
    }

    /**
     * Transfer quote items between stores
     *
     * @return void
     */
    protected function transferQuote()
    {
        try {
            $quote = $this->cart->getQuote();

            if (!$quote->hasItems()) {
                return;
            }

            foreach ($quote->getAllItems() as $item) {
                $currentStoreId = $item->getStoreId();

                // Transfer from store 1 to store 3
                if ($currentStoreId == 1) {
                    $item->setStoreId(3);
                    $item->save();
                }
                // Transfer from store 2 to store 4
                elseif ($currentStoreId == 2) {
                    $item->setStoreId(4);
                    $item->save();
                }
            }

            $quoteStoreId = $quote->getStoreId();

            // Transfer quote from store 1 to store 3
            if ($quoteStoreId == 1) {
                $quote->setStoreId(3);
                $quote->save();
            }
            // Transfer quote from store 2 to store 4
            elseif ($quoteStoreId == 2) {
                $quote->setStoreId(4);
                $quote->save();
            }
        } catch (\Exception $e) {
            $this->logger->error('B2B Quote Transfer Error: ' . $e->getMessage());
        }
    }
}
