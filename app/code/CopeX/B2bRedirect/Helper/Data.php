<?php
/**
 * CopeX B2B Redirect Helper
 *
 * @category  CopeX
 * @package   CopeX_B2bRedirect
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

namespace CopeX\B2bRedirect\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    /**
     * Configuration paths
     */
    const XML_PATH_ENABLED = 'copex_b2b_redirect/general/enabled';
    const XML_PATH_CUSTOMER_GROUPS = 'copex_b2b_redirect/general/customer_groups';
    const XML_PATH_B2B_BASE_URL = 'copex_b2b_redirect/general/b2b_base_url';
    const XML_PATH_B2C_BASE_URL = 'copex_b2b_redirect/general/b2c_base_url';
    const XML_PATH_PROSPRO_WEBSITE_CODE = 'copex_b2b_redirect/general/prospro_website_code';
    const XML_PATH_ARFAIAN_WEBSITE_CODE = 'copex_b2b_redirect/general/arfaian_website_code';
    const XML_PATH_MINIMUM_ORDER_RULES = 'sales/minimum_order/customer_group_rules';

    /**
     * @var UrlInterface
     */
    protected $urlBuilder;

    /**
     * Data constructor.
     * @param Context $context
     * @param UrlInterface $urlBuilder
     */
    public function __construct(
        Context $context,
        UrlInterface $urlBuilder
    ) {
        parent::__construct($context);
        $this->urlBuilder = $urlBuilder;
    }

    /**
     * Check if module is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isEnabled($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get current URL path part
     *
     * @return string
     */
    public function getUrlPart()
    {
        $currentUrl = $this->urlBuilder->getCurrentUrl();
        $parsedUrl = parse_url($currentUrl);
        $path = isset($parsedUrl['path']) ? ltrim($parsedUrl['path'], '/') : '';

        return $path;
    }

    /**
     * Get redirect URL
     *
     * @param bool $isB2b
     * @return string
     */
    public function getRedirectUrl($isB2b = false)
    {
        if ($isB2b) {
            $baseUrl = $this->scopeConfig->getValue(
                self::XML_PATH_B2B_BASE_URL,
                ScopeInterface::SCOPE_STORE
            );
        } else {
            $baseUrl = $this->scopeConfig->getValue(
                self::XML_PATH_B2C_BASE_URL,
                ScopeInterface::SCOPE_STORE
            );
        }

        $urlPart = $this->getUrlPart();
        return rtrim($baseUrl, '/') . '/' . $urlPart;
    }

    /**
     * Get B2B customer groups
     *
     * @return array
     */
    public function getCustomerGroups()
    {
        $groups = $this->scopeConfig->getValue(
            self::XML_PATH_CUSTOMER_GROUPS,
            ScopeInterface::SCOPE_STORE
        );

        return $groups ? explode(',', $groups) : [];
    }

    /**
     * Get ProsPro website code
     *
     * @return string
     */
    public function getProsProWebsiteCode()
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_PROSPRO_WEBSITE_CODE,
            ScopeInterface::SCOPE_STORE
        ) ?: 'prospro';
    }

    /**
     * Get Arfaian website code
     *
     * @return string
     */
    public function getArfaianWebsiteCode()
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_ARFAIAN_WEBSITE_CODE,
            ScopeInterface::SCOPE_STORE
        ) ?: 'arfaian';
    }

    /**
     * Get minimum order value by customer group ID
     *
     * @param int $groupId
     * @return float|false
     */
    public function getMinimumOrderByGroupId($groupId)
    {
        $config = $this->scopeConfig->getValue(
            self::XML_PATH_MINIMUM_ORDER_RULES,
            ScopeInterface::SCOPE_STORE
        );

        if (!$config) {
            return false;
        }

        $rows = explode(PHP_EOL, $config);

        foreach ($rows as $row) {
            $row = explode(':', trim($row));
            if (count($row) < 2) {
                continue;
            }

            $groups = explode(',', $row[0]);

            if (in_array($groupId, $groups)) {
                return (float)$row[1];
            }
        }

        return false;
    }
}
