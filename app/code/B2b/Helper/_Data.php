<?php

class Mstage_B2b_Helper_Data extends Mage_Core_Helper_Abstract {

    public function getUrlPart()
    {
        $urlString = Mage::helper('core/url')->getCurrentUrl();
        $url = Mage::getSingleton('core/url')->parseUrl($urlString);     
        $path = substr($url->getPath(), 1 );
        
        return $path;
    }
    
    public function getRedirectUrl($btb = false) {
        $baseUrl = Mage::getBaseUrl();
        
        if ($btb) {
            $baseUrl = "http://b2b.prospro-tennisversand.com";
        }
        else {
            $baseUrl = "http://www.prospro-tennisversand.com"; 
        }
        
        return $baseUrl.DS.$this->getUrlPart();
    }
}