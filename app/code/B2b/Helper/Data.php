<?php

class Mstage_B2b_Helper_Data extends Mage_Core_Helper_Abstract
{

    public function getUrlPart()
    {
        $urlString = Mage::helper('core/url')->getCurrentUrl();
        $url = Mage::getSingleton('core/url')->parseUrl($urlString);
        $path = substr($url->getPath(), 1);

        return $path;
    }

    public function getRedirectUrl($btb = false)
    {
        $baseUrl = Mage::getBaseUrl();

        if ($btb) {
            $baseUrl = "https://b2b.pros-pro.com";
        } else {
            $baseUrl = "https://www.pros-pro.com";
        }

        return $baseUrl . DS . $this->getUrlPart();
    }

    public function getCustomerGroups()
    {
        $groups = Mage::getStoreConfig('mstage_b2b/general/c_groups');

        return explode(',', $groups);
    }

    public function getMinimumOrderByGroupId($id)
    {
        $config = Mage::getStoreConfig('sales/minimum_order/groups');

        $rows = explode(PHP_EOL, $config);

        foreach ($rows as $row) {
            $row = explode(':', trim($row));
            $groups = explode(',', $row[0]);

            if (in_array($id, $groups)) {
                return $row[1];
            }
        }

        return false;
    }
}
