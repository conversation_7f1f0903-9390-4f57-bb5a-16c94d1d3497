<?php

class Mstage_B2b_Model_Observer
{
    protected $_singletonFlag = false;
    
    protected $_customerSession;
    
    public function checkForward($observer) {
        
        if (!$this->_singletonFlag) {
            
            $this->_singletonFlag = true;
            
            
            $this->_customerSession = $observer->getCustomerSession();
        
            $websitecode = Mage::app()->getWebsite()->getCode();

            if ($websitecode == 'prospro' && $this->isB2bCustomer()) {
                $this->transferQuote();
                header('Location: '.Mage::helper('mstage_b2b')->getRedirectUrl(true));
                die();
//                Mage::app()->getResponse()->setRedirect(Mage::helper('mstage_b2b')->getRedirectUrl(true));
                
            }
            elseif ($websitecode == 'arfaian' && !$this->isB2bCustomer()) {
                Mage::app()->getResponse()->setRedirect(Mage::helper('mstage_b2b')->getRedirectUrl());
            }
        }
    }
    
    protected function isB2bCustomer() {
        // Get group Id
        $groupId = $this->_customerSession->getCustomerGroupId();
        $allowedCustomerGroups = Mage::helper('mstage_b2b')->getCustomerGroups();

        if (in_array($groupId, $allowedCustomerGroups)) {
            return true;
        }

        return false;
    }
    
    public function transferQuote() {
        
        $quote = Mage::getModel('checkout/cart')->getQuote();
        
        if ($quote->hasItems()) {
            foreach ($quote->getAllItems() as $item) {
                if ($item->getStoreId() == 1) {
                    $item->setStoreId(3);
                    $item->save();
                }
                else if ($item->getStoreId() == 2) {
                    $item->setStoreId(4);
                    $item->save();
                }
            }

            if ($quote->getStoreId() == 1) {
                $quote->setStoreId(3);
                $quote->save();
            }
            else if ($quote->getStoreId() == 2) {
                $quote->setStoreId(4);
                $quote->save();
            }
        }
    }
}
