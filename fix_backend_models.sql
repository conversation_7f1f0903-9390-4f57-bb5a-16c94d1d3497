-- Fix for ReflectionException: Class "Magento\Catalog\Model\Product\Attribute\Backend\Url" does not exist
-- This SQL script removes invalid backend models from product attributes

-- First, let's see what we're dealing with
SELECT attribute_id, attribute_code, backend_model 
FROM eav_attribute 
WHERE entity_type_id = 4 
  AND backend_model IS NOT NULL 
  AND backend_model != ''
  AND (
    backend_model LIKE '%Url%' 
    OR backend_model = 'Magento\Catalog\Model\Product\Attribute\Backend\Url'
    OR backend_model = 'catalog/product_attribute_backend_url'
  );

-- Fix the invalid backend models by setting them to NULL
UPDATE eav_attribute 
SET backend_model = NULL 
WHERE entity_type_id = 4 
  AND (
    backend_model = 'Magento\Catalog\Model\Product\Attribute\Backend\Url'
    OR backend_model = 'catalog/product_attribute_backend_url'
  );

-- Verify the fix
SELECT COUNT(*) as fixed_attributes
FROM eav_attribute 
WHERE entity_type_id = 4 
  AND backend_model IS NULL 
  AND attribute_code IN (
    SELECT attribute_code 
    FROM eav_attribute 
    WHERE backend_model = 'Magento\Catalog\Model\Product\Attribute\Backend\Url'
       OR backend_model = 'catalog/product_attribute_backend_url'
  );
