<?php
/**
 * Fix for ReflectionException: Class "Magento\Catalog\Model\Product\Attribute\Backend\Url" does not exist
 * This script finds and fixes product attributes with invalid backend models
 */

use Magento\Framework\App\Bootstrap;

require __DIR__ . '/app/bootstrap.php';

$bootstrap = Bootstrap::create(BP, $_SERVER);
$objectManager = $bootstrap->getObjectManager();

$state = $objectManager->get(\Magento\Framework\App\State::class);
$state->setAreaCode('adminhtml');

$resource = $objectManager->get(\Magento\Framework\App\ResourceConnection::class);
$connection = $resource->getConnection();

echo "Checking for invalid backend models in product attributes...\n";

// Get all product attributes with backend models
$select = $connection->select()
    ->from('eav_attribute', ['attribute_id', 'attribute_code', 'backend_model'])
    ->where('entity_type_id = ?', 4) // Product entity type ID is typically 4
    ->where('backend_model IS NOT NULL')
    ->where('backend_model != ""');

$attributes = $connection->fetchAll($select);

$fixedCount = 0;
$invalidBackendModels = [
    'Magento\Catalog\Model\Product\Attribute\Backend\Url',
    'catalog/product_attribute_backend_url',
];

foreach ($attributes as $attribute) {
    $backendModel = $attribute['backend_model'];
    
    // Check if this is one of the known invalid backend models
    if (in_array($backendModel, $invalidBackendModels)) {
        echo "Found invalid backend model: {$backendModel} for attribute: {$attribute['attribute_code']} (ID: {$attribute['attribute_id']})\n";
        
        // Remove the invalid backend model
        $connection->update(
            'eav_attribute',
            ['backend_model' => null],
            ['attribute_id = ?' => $attribute['attribute_id']]
        );
        
        echo "Fixed attribute: {$attribute['attribute_code']}\n";
        $fixedCount++;
    } else {
        // Check if the class actually exists
        if (!class_exists($backendModel)) {
            echo "Found non-existent backend model: {$backendModel} for attribute: {$attribute['attribute_code']} (ID: {$attribute['attribute_id']})\n";
            
            // Remove the invalid backend model
            $connection->update(
                'eav_attribute',
                ['backend_model' => null],
                ['attribute_id = ?' => $attribute['attribute_id']]
            );
            
            echo "Fixed attribute: {$attribute['attribute_code']}\n";
            $fixedCount++;
        }
    }
}

echo "\nFixed {$fixedCount} attributes with invalid backend models.\n";

if ($fixedCount > 0) {
    echo "\nPlease run the following commands to clear cache:\n";
    echo "php bin/magento cache:clean\n";
    echo "php bin/magento cache:flush\n";
    echo "php bin/magento indexer:reindex\n";
}

echo "\nDone!\n";
